"use strict";
/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
var io_utils_1 = require("./io_utils");
describe('toBuffer', function () {
    it('Simple case', function () {
        var ab = new Uint8Array([3, 2, 1]).buffer;
        var buffer = (0, io_utils_1.toBuffer)(ab);
        expect(new Uint8Array(buffer)).toEqual(new Uint8Array([3, 2, 1]));
    });
});
describe('toArrayBuffer', function () {
    it('Single Buffer', function () {
        var buf = Buffer.from([10, 20, 30]);
        var ab = (0, io_utils_1.toArrayBuffer)(buf);
        expect(new Uint8Array(ab)).toEqual(new Uint8Array([10, 20, 30]));
    });
    it('Two Buffers', function () {
        var buf1 = Buffer.from([10, 20, 30]);
        var buf2 = Buffer.from([40, 50, 60]);
        var ab = (0, io_utils_1.toArrayBuffer)([buf1, buf2]);
        expect(new Uint8Array(ab)).toEqual(new Uint8Array([
            10, 20, 30, 40, 50, 60
        ]));
    });
    it('Three Buffers', function () {
        var buf1 = Buffer.from([10, 20, 30]);
        var buf2 = Buffer.from([40, 50, 60]);
        var buf3 = Buffer.from([3, 2, 1]);
        var ab = (0, io_utils_1.toArrayBuffer)([buf1, buf2, buf3]);
        expect(new Uint8Array(ab)).toEqual(new Uint8Array([
            10, 20, 30, 40, 50, 60, 3, 2, 1
        ]));
    });
    it('Zero buffers', function () {
        var ab = (0, io_utils_1.toArrayBuffer)([]);
        expect(new Uint8Array(ab)).toEqual(new Uint8Array([]));
    });
});
