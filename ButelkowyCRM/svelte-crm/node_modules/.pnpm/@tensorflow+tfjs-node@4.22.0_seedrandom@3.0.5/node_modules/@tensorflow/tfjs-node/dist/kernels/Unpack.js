"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.unpackConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.unpackConfig = {
    kernelName: tfjs_1.Unpack,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var value = args.inputs.value;
        var backend = args.backend;
        var axis = args.attrs.axis;
        if (axis < 0) {
            axis += value.shape.length;
        }
        if (axis >= value.shape.length) {
            throw new Error("Invalid axis supplied: ".concat(axis, " shape length: ").concat(value.shape.length));
        }
        var num = value.shape[axis];
        var opAttrs = [
            { name: 'num', type: backend.binding.TF_ATTR_INT, value: num },
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', value.dtype),
            { name: 'axis', type: backend.binding.TF_ATTR_INT, value: axis }
        ];
        return backend.executeMultipleOutputs(tfjs_1.Unpack, opAttrs, [value], num);
    }
};
