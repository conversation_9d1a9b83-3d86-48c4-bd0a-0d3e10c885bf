{"version": 3, "file": "custom_module.js", "sourceRoot": "", "sources": ["../../../tools/custom_module/custom_module.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAGH,+BAAmC;AAEnC,SAAgB,qBAAqB,CACjC,MAA8B,EAC9B,cAA8B;IAEzB,IAAA,OAAO,GAAuC,MAAM,QAA7C,EAAE,QAAQ,GAA6B,MAAM,SAAnC,EAAE,eAAe,GAAY,MAAM,gBAAlB,EAAE,MAAM,GAAI,MAAM,OAAV,CAAW;IAC5D,IAAM,IAAI,GAAa,CAAC,IAAA,kBAAW,GAAE,CAAC,CAAC;IAEvC,uBAAuB;IACvB,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC;IAC7D,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACrB,gCAAgC;QAChC,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,kBAAkB,EAAE,CAAC,CAAC;KACpD;IAED,KAAsB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE;QAA3B,IAAM,OAAO,iBAAA;QAChB,OAAO,CAAC,IAAI,EAAE,wBAAiB,OAAO,CAAE,CAAC,CAAC;QAC1C,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;QACxD,KAAyB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;YAA7B,IAAM,UAAU,gBAAA;YACnB,IAAM,YAAY,GAAG,cAAc,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACzE,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;gBAC/D,OAAO,CAAC,IAAI,CACR,UAAU,EACV,uBACI,YAAY,CAAC,UAAU,sCAAmC,CAAC,CAAC;gBACpE,SAAS;aACV;YACD,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,CAAC,IAAI,EAAE,iBAAiB,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC;SAC/D;KACF;IAED,IAAI,CAAC,eAAe,EAAE;QACpB,OAAO,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAC/B,KAAyB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;YAA7B,IAAM,UAAU,gBAAA;YACnB,IAAM,UAAU,GAAG,cAAc,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YACtE,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;gBAC7D,OAAO,CAAC,IAAI,CACR,UAAU,EACV,uBACI,UAAU,CAAC,UAAU,sCAAmC,CAAC,CAAC;gBAClE,SAAS;aACV;YACD,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC;YAC1C,OAAO,CAAC,IAAI,EAAE,yBAAyB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;SACnE;KACF;IAED,6DAA6D;IAC7D,IAAM,IAAI,GAAa,CAAC,IAAA,kBAAW,GAAE,CAAC,CAAC;IACvC,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC;IAC7D,OAAO;QACL,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACrB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;KACtB,CAAC;AACJ,CAAC;AAtDD,sDAsDC;AAED,SAAgB,2BAA2B,CACvC,GAAa,EAAE,cAA8B;IAC/C,IAAM,MAAM,GAAa,CAAC,iCAAiC,CAAC,CAAC;IAE7D,sEAAsE;IACtE,kEAAkE;IAElE,IAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAM,aAAa,GAA8B,EAAE,CAAC;IAEpD,KAAuB,UAAG,EAAH,WAAG,EAAH,iBAAG,EAAH,IAAG,EAAE;QAAvB,IAAM,QAAQ,YAAA;QACjB,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACxB,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACnC,IAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,IAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAExB,IAAI,aAAa,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE;gBACpC,aAAa,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;aAC/B;YACD,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACvC;aAAM;YACL,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACxB;KACF;IAED,4CAA4C;IAC5C,KAAwB,UAA0B,EAA1B,KAAA,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAA1B,cAA0B,EAA1B,IAA0B,EAAE;QAA/C,IAAM,SAAS,SAAA;QAClB,IAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,kCAAkC,CACzD,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;KAC5B;IAED,KAAuB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;QAA3B,IAAM,QAAQ,gBAAA;QACjB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC/D;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AArCD,kEAqCC;AAED,SAAS,OAAO,CAAC,MAAgB,EAAE,IAAY;IAC7C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpB,CAAC;AAED,SAAS,iBAAiB,CAAC,cAAsB;IAC/C,OAAO,yBAAkB,cAAc,OAAI,CAAC;AAC9C,CAAC;AAED,SAAS,yBAAyB,CAAC,YAAoB;IACrD,OAAO,2BAAoB,YAAY,OAAI,CAAC;AAC9C,CAAC"}