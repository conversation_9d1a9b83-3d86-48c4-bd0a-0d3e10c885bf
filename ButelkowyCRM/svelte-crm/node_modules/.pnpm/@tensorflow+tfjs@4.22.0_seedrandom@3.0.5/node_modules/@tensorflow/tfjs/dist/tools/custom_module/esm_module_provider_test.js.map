{"version": 3, "file": "esm_module_provider_test.js", "sourceRoot": "", "sources": ["../../../tools/custom_module/esm_module_provider_test.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAEH,6DAAwD;AAExD,QAAQ,CAAC,qBAAqB,EAAE;IAC9B,EAAE,CAAC,oCAAoC,EAAE;QACvC,IAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,IAAM,GAAG,GAAG,uCAAiB,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAC7D,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CACjB,gEAAgE,CAAC,CAAC;QACtE,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CACrB,kEAAkE,CAAC,CAAC;QACxE,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CACjB,wDAAwD,CAAC,CAAC;QAC9D,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,kDAAkD,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE;QACxC,IAAM,eAAe,GAAG,KAAK,CAAC;QAC9B,IAAM,GAAG,GAAG,uCAAiB,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAC7D,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CACjB,gEAAgE,CAAC,CAAC;QACtE,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CACjB,kEAAkE,CAAC,CAAC;QACxE,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CACjB,wDAAwD,CAAC,CAAC;QAC9D,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,kDAAkD,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oBAAoB,EAAE;QACvB,IAAM,GAAG,GAAG,uCAAiB,CAAC,kBAAkB,EAAE,CAAC;QACnD,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sBAAsB,EAAE;QACzB,IAAM,GAAG,GAAG,uCAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACtD,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wBAAwB,EAAE;QAC3B,IAAM,GAAG,GAAG,uCAAiB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACxD,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACZ,2DAA2D,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yBAAyB,EAAE;QAC5B,IAAM,GAAG,GAAG,uCAAiB,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC5D,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QACvE,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC;aACtB,SAAS,CAAC,sDAAsD,CAAC,CAAC;QAEvE,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6BAA6B,EAAE;QAChC,IAAM,GAAG,GAAG,uCAAiB,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAC7D,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QAChE,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC;aACtB,SAAS,CAAC,sDAAsD,CAAC,CAAC;QAEvE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6BAA6B,EAAE;QAChC,IAAM,GAAG,GAAG,uCAAiB,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAC7D,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;QAChE,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC;aACtB,SAAS,CAAC,sDAAsD,CAAC,CAAC;QAEvE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6BAA6B,EAAE;QAChC,IAAM,GAAG,GAAG,uCAAiB,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAC7D,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE;QACrC,IAAM,GAAG,GAAG,uCAAiB,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QAClE,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACZ,oEAAoE,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wCAAwC,EAAE;QAC3C,IAAM,GAAG,GAAG,uCAAiB,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;QACxE,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;QACZ,2CAA2C;QAC3C,kFAAkF,CAAC,CAAC;IAC1F,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,kCAAkC,EAAE;QACrC,IAAM,GAAG,GAAG,uCAAiB,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QAClE,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CACZ,oEAAoE,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mCAAmC,EAAE;QACtC,IAAM,GAAG,GAAG,uCAAiB,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QACnE,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;QACZ,2CAA2C;QAC3C,uEAAuE,CAAC,CAAC;IAC/E,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sCAAsC,EAAE;QACzC,IAAM,GAAG,GAAG,uCAAiB,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;QACtE,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;QACZ,4CAA4C;QAC5C,4EAA4E,CAAC,CAAC;IACpF,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yDAAyD,EAAE;QAC5D,IAAM,GAAG,GAAG,uCAAiB,CAAC,kCAAkC,CAC5D,OAAO,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACjC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;QACZ,4CAA4C;QAC5C,mLAGL,CAAC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,yDAAyD,EAAE;QAC5D,IAAM,GAAG,GAAG,uCAAiB,CAAC,kCAAkC,CAC5D,OAAO,EAAE,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,CAAC,CAAC;QAC1D,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI;QACZ,4CAA4C;QAC5C,8WAKL,CAAC,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}