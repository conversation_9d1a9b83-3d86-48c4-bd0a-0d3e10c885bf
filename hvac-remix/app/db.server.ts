/**
 * Database Server Module - GoBackend-Kratos Integration
 * Provides centralized database operations through GoBackend-Kratos API
 */

// Temporary mock client until GoBackend-Kratos is fully integrated
const gobackendClient = {
  customer: {
    list: { query: async (args?: any) => ({ customers: [], total: 0 }) },
    get: { query: async (args: any) => null },
    create: { mutate: async (args: any) => ({}) },
    update: { mutate: async (args: any) => ({}) },
    delete: { mutate: async (args: any) => ({}) }
  },
  job: {
    list: { query: async (args?: any) => ({ jobs: [], total: 0 }) },
    get: { query: async (args: any) => null },
    create: { mutate: async (args: any) => ({}) },
    update: { mutate: async (args: any) => ({}) },
    delete: { mutate: async (args: any) => ({}) }
  }
};

declare global {
  var __db__: typeof apiClient;
}

// API client wrapper that mimics Prisma interface for compatibility
const apiClient = {
  // Customer operations
  customer: {
    findMany: async (args?: any) => {
      try {
        const result = await gobackendClient.customer.list.query(args);
        return result.customers || [];
      } catch (error) {
        console.error('Error fetching customers:', error);
        return [];
      }
    },
    findUnique: async (args: { where: { id: string } }) => {
      try {
        return await gobackendClient.customer.get.query({ id: args.where.id });
      } catch (error) {
        console.error('Error fetching customer:', error);
        return null;
      }
    },
    findFirst: async (args?: any) => {
      try {
        const result = await gobackendClient.customer.list.query({ ...args, limit: 1 });
        return result.customers?.[0] || null;
      } catch (error) {
        console.error('Error fetching first customer:', error);
        return null;
      }
    },
    create: async (args: { data: any }) => {
      try {
        return await gobackendClient.customer.create.mutate(args.data);
      } catch (error) {
        console.error('Error creating customer:', error);
        throw error;
      }
    },
    update: async (args: { where: { id: string }, data: any }) => {
      try {
        return await gobackendClient.customer.update.mutate({
          id: args.where.id,
          ...args.data
        });
      } catch (error) {
        console.error('Error updating customer:', error);
        throw error;
      }
    },
    delete: async (args: { where: { id: string } }) => {
      try {
        return await gobackendClient.customer.delete.mutate({ id: args.where.id });
      } catch (error) {
        console.error('Error deleting customer:', error);
        throw error;
      }
    },
    count: async () => {
      try {
        const result = await gobackendClient.customer.list.query({ limit: 1 });
        return result.total || 0;
      } catch (error) {
        console.error('Error counting customers:', error);
        return 0;
      }
    }
  },

  // Job operations
  job: {
    findMany: async (args?: any) => {
      try {
        const result = await gobackendClient.job.list.query(args);
        return result.jobs || [];
      } catch (error) {
        console.error('Error fetching jobs:', error);
        return [];
      }
    },
    findUnique: async (args: { where: { id: string } }) => {
      try {
        return await gobackendClient.job.get.query({ id: args.where.id });
      } catch (error) {
        console.error('Error fetching job:', error);
        return null;
      }
    },
    create: async (args: { data: any }) => {
      try {
        return await gobackendClient.job.create.mutate(args.data);
      } catch (error) {
        console.error('Error creating job:', error);
        throw error;
      }
    },
    update: async (args: { where: { id: string }, data: any }) => {
      try {
        return await gobackendClient.job.update.mutate({
          id: args.where.id,
          ...args.data
        });
      } catch (error) {
        console.error('Error updating job:', error);
        throw error;
      }
    },
    delete: async (args: { where: { id: string } }) => {
      try {
        return await gobackendClient.job.delete.mutate({ id: args.where.id });
      } catch (error) {
        console.error('Error deleting job:', error);
        throw error;
      }
    },
    count: async () => {
      try {
        const result = await gobackendClient.job.list.query({ limit: 1 });
        return result.total || 0;
      } catch (error) {
        console.error('Error counting jobs:', error);
        return 0;
      }
    }
  },

  // User operations
  user: {
    findMany: async (args?: any) => {
      try {
        console.log('User.findMany called with args:', args);
        return [];
      } catch (error) {
        console.error('Error fetching users:', error);
        return [];
      }
    },
    findUnique: async (args: { where: { id?: string; email?: string } }) => {
      try {
        console.log('User.findUnique called with args:', args);
        return null;
      } catch (error) {
        console.error('Error fetching user:', error);
        return null;
      }
    },
    create: async (args: any) => {
      try {
        console.log('User.create called with args:', args);
        return null;
      } catch (error) {
        console.error('Error creating user:', error);
        return null;
      }
    },
    update: async (args: any) => {
      try {
        console.log('User.update called with args:', args);
        return null;
      } catch (error) {
        console.error('Error updating user:', error);
        return null;
      }
    },
    count: async (args?: any) => {
      try {
        console.log('User.count called with args:', args);
        return 0;
      } catch (error) {
        console.error('Error counting users:', error);
        return 0;
      }
    }
  },

  // Device operations
  device: {
    findMany: async (args?: any) => {
      try {
        console.log('Device.findMany called with args:', args);
        return [];
      } catch (error) {
        console.error('Error fetching devices:', error);
        return [];
      }
    },
    findUnique: async (args: { where: { id: string } }) => {
      try {
        console.log('Device.findUnique called with args:', args);
        return null;
      } catch (error) {
        console.error('Error fetching device:', error);
        return null;
      }
    },
    create: async (args: any) => {
      try {
        console.log('Device.create called with args:', args);
        return null;
      } catch (error) {
        console.error('Error creating device:', error);
        return null;
      }
    },
    update: async (args: any) => {
      try {
        console.log('Device.update called with args:', args);
        return null;
      } catch (error) {
        console.error('Error updating device:', error);
        return null;
      }
    },
    delete: async (args: any) => {
      try {
        console.log('Device.delete called with args:', args);
        return null;
      } catch (error) {
        console.error('Error deleting device:', error);
        return null;
      }
    },
    count: async (args?: any) => {
      try {
        console.log('Device.count called with args:', args);
        return 0;
      } catch (error) {
        console.error('Error counting devices:', error);
        return 0;
      }
    }
  },

  // Communication Template operations
  communicationTemplate: {
    findMany: async (args?: any) => {
      try {
        // Return empty array for now - implement when backend is ready
        return [];
      } catch (error) {
        console.error('Error fetching communication templates:', error);
        return [];
      }
    },
    findUnique: async (args: { where: { id: string } }) => {
      try {
        return null;
      } catch (error) {
        console.error('Error fetching communication template:', error);
        return null;
      }
    },
    create: async (args: { data: any }) => {
      try {
        return { id: 'mock-template-id', ...args.data };
      } catch (error) {
        console.error('Error creating communication template:', error);
        throw error;
      }
    }
  },

  // User MFA operations
  userMFA: {
    findUnique: async (args: { where: { userId: string } }) => {
      try {
        // Return mock MFA config
        return {
          userId: args.where.userId,
          secret: 'mock-secret',
          backupCodes: [],
          isEnabled: false
        };
      } catch (error) {
        console.error('Error fetching user MFA config:', error);
        return null;
      }
    },
    upsert: async (args: any) => {
      try {
        // Mock upsert operation
        return {
          userId: args.where.userId,
          ...args.create,
          ...args.update
        };
      } catch (error) {
        console.error('Error upserting MFA config:', error);
        throw error;
      }
    },
    update: async (args: { where: { userId: string }, data: any }) => {
      try {
        // Mock update operation
        return {
          userId: args.where.userId,
          ...args.data
        };
      } catch (error) {
        console.error('Error updating MFA config:', error);
        throw error;
      }
    },
    delete: async (args: { where: { userId: string } }) => {
      try {
        // Mock delete operation
        return {
          userId: args.where.userId
        };
      } catch (error) {
        console.error('Error deleting MFA config:', error);
        throw error;
      }
    }
  },

  // Communication operations
  communication: {
    findMany: async (args?: any) => {
      try {
        // Return empty array for now - implement when backend is ready
        return [];
      } catch (error) {
        console.error('Error fetching communications:', error);
        return [];
      }
    },
    findUnique: async (args: { where: { id: string } }) => {
      try {
        return null;
      } catch (error) {
        console.error('Error fetching communication:', error);
        return null;
      }
    },
    create: async (args: { data: any }) => {
      try {
        return {
          id: 'mock-communication-id',
          timestamp: new Date(),
          read: false,
          ...args.data
        };
      } catch (error) {
        console.error('Error creating communication:', error);
        throw error;
      }
    },
    update: async (args: { where: { id: string }, data: any }) => {
      try {
        return { id: args.where.id, ...args.data };
      } catch (error) {
        console.error('Error updating communication:', error);
        throw error;
      }
    }
  },

  // Transaction support (simplified)
  $transaction: async (fn: (client: typeof apiClient) => Promise<any>) => {
    try {
      return await fn(apiClient);
    } catch (error) {
      console.error('Transaction failed:', error);
      throw error;
    }
  },

  // Password operations
  password: {
    findUnique: async (args: { where: { userId: string } }) => {
      try {
        console.log('Password.findUnique called with args:', args);
        return null;
      } catch (error) {
        console.error('Error fetching password:', error);
        return null;
      }
    },
    create: async (args: any) => {
      try {
        console.log('Password.create called with args:', args);
        return null;
      } catch (error) {
        console.error('Error creating password:', error);
        return null;
      }
    },
    update: async (args: any) => {
      try {
        console.log('Password.update called with args:', args);
        return null;
      } catch (error) {
        console.error('Error updating password:', error);
        return null;
      }
    },
  },

  // Invoice operations
  invoice: {
    findMany: async (args?: any) => {
      try {
        console.log('Invoice.findMany called with args:', args);
        return [];
      } catch (error) {
        console.error('Error fetching invoices:', error);
        return [];
      }
    },
    count: async (args?: any) => {
      try {
        console.log('Invoice.count called with args:', args);
        return 0;
      } catch (error) {
        console.error('Error counting invoices:', error);
        return 0;
      }
    }
  },

  // Opportunity operations
  opportunity: {
    findMany: async (args?: any) => {
      try {
        console.log('Opportunity.findMany called with args:', args);
        return [];
      } catch (error) {
        console.error('Error fetching opportunities:', error);
        return [];
      }
    },
    count: async (args?: any) => {
      try {
        console.log('Opportunity.count called with args:', args);
        return 0;
      } catch (error) {
        console.error('Error counting opportunities:', error);
        return 0;
      }
    }
  },

  // Service Order operations
  serviceOrder: {
    findMany: async (args?: any) => {
      try {
        // Mock implementation for now - replace with actual GoBackend API call
        console.log('ServiceOrder.findMany called with args:', args);
        return [];
      } catch (error) {
        console.error('Error fetching service orders:', error);
        return [];
      }
    },
    findUnique: async (args: { where: { id: string } }) => {
      try {
        console.log('ServiceOrder.findUnique called with args:', args);
        return null;
      } catch (error) {
        console.error('Error fetching service order:', error);
        return null;
      }
    },
    create: async (args: any) => {
      try {
        console.log('ServiceOrder.create called with args:', args);
        return null;
      } catch (error) {
        console.error('Error creating service order:', error);
        return null;
      }
    },
    update: async (args: any) => {
      try {
        console.log('ServiceOrder.update called with args:', args);
        return null;
      } catch (error) {
        console.error('Error updating service order:', error);
        return null;
      }
    },
    delete: async (args: any) => {
      try {
        console.log('ServiceOrder.delete called with args:', args);
        return null;
      } catch (error) {
        console.error('Error deleting service order:', error);
        return null;
      }
    },
    count: async (args?: any) => {
      try {
        console.log('ServiceOrder.count called with args:', args);
        return 0;
      } catch (error) {
        console.error('Error counting service orders:', error);
        return 0;
      }
    },
    groupBy: async (args: any) => {
      try {
        console.log('ServiceOrder.groupBy called with args:', args);
        return [];
      } catch (error) {
        console.error('Error grouping service orders:', error);
        return [];
      }
    },
  },

  // Raw query support (limited)
  $queryRaw: async (query: any) => {
    try {
      // For health checks, just return success
      if (query.strings && query.strings[0] === 'SELECT 1') {
        return [{ '?column?': 1 }];
      }
      throw new Error('Raw queries not supported with GoBackend-Kratos API');
    } catch (error) {
      console.error('Raw query failed:', error);
      throw error;
    }
  },

  // Connection management
  $connect: async () => {
    console.log('🔌 Connected to GoBackend-Kratos API');
  },

  $disconnect: async () => {
    console.log('🔌 Disconnected from GoBackend-Kratos API');
  }
};

let prisma: typeof apiClient;

// Initialize the API client
if (process.env.NODE_ENV === 'production') {
  prisma = apiClient;
} else {
  if (!global.__db__) {
    global.__db__ = apiClient;
  }
  prisma = global.__db__;
}

console.log('🔌 Setting up GoBackend-Kratos API client');

/**
 * Database connection with connection pooling and error handling
 */
export { prisma };

/**
 * Health check for database connection
 */
export async function healthCheck() {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    console.error('GoBackend-Kratos API health check failed:', error);
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Graceful shutdown
 */
export async function shutdown() {
  await prisma.$disconnect();
}

/**
 * Database transaction wrapper with error handling
 */
export async function withTransaction<T>(
  fn: (prisma: typeof apiClient) => Promise<T>
): Promise<T> {
  try {
    return await prisma.$transaction(fn);
  } catch (error) {
    console.error('Transaction failed:', error);
    throw error;
  }
}

/**
 * Database metrics for monitoring
 */
export async function getDatabaseMetrics() {
  try {
    const [
      userCount,
      customerCount,
      jobCount,
      deviceCount
    ] = await Promise.all([
      prisma.user.count(),
      prisma.customer.count(),
      prisma.job.count(),
      prisma.device.count()
    ]);

    return {
      users: userCount,
      customers: customerCount,
      jobs: jobCount,
      devices: deviceCount,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Failed to get database metrics:', error);
    throw error;
  }
}

/**
 * Database cleanup utilities (simplified for API)
 */
export const cleanup = {
  /**
   * Clean up old sessions
   */
  async sessions(olderThanDays = 30) {
    console.log(`Cleanup sessions older than ${olderThanDays} days - not implemented with API`);
    return { count: 0 };
  },

  /**
   * Clean up old logs
   */
  async logs(olderThanDays = 90) {
    console.log(`Cleanup logs older than ${olderThanDays} days - not implemented with API`);
    return { count: 0 };
  }
};

// Export default for compatibility
export default prisma;
